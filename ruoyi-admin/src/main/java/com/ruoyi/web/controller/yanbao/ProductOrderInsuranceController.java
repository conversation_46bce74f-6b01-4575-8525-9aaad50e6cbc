package com.ruoyi.web.controller.yanbao;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceVo;
import com.ruoyi.yanbao.service.ProductOrderInsuranceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/yanbao/insurance")
public class ProductOrderInsuranceController extends BaseController {

    @Autowired
    private ProductOrderInsuranceService productOrderInsuranceService;

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insurance:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductOrderInsuranceVo param) {
        startPage();
        List<ProductOrderInsuranceVo> list = productOrderInsuranceService.queryList(param);
        return getDataTable(list);
    }
    
}
