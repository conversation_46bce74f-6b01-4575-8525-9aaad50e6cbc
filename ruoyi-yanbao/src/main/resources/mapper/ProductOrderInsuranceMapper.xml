<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.yanbao.mapper.ProductOrderInsuranceMapper">

    <resultMap id="voResultMap" type="com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceVo">
        <result column="id" property="id"/>
    </resultMap>

    <select id="queryList" parameterType="com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceVo" resultMap="voResultMap">
        SELECT a.id
        from p_product_order_insurance a,p_product_order b where
        a.is_delete=0 and a.product_order_id=b.id
    </select>
</mapper>
