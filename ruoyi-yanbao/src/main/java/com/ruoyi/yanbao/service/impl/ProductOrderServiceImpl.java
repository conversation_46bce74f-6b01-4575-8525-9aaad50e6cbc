package com.ruoyi.yanbao.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.exception.base.BaseException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.SysFile;
import com.ruoyi.system.service.ISysFileService;
import com.ruoyi.yanbao.entity.ProductOrder;
import com.ruoyi.yanbao.entity.vo.ProductOrderVo;
import com.ruoyi.yanbao.mapper.ProductOrderMapper;
import com.ruoyi.yanbao.service.ProductOrderInsuranceService;
import com.ruoyi.yanbao.service.ProductOrderService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
public class ProductOrderServiceImpl extends ServiceImpl<ProductOrderMapper, ProductOrder> implements ProductOrderService {

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private ProductOrderInsuranceService productOrderInsuranceService;

    @Override
    public List<ProductOrder> getAllSaleUser() {
        return baseMapper.getAllSaleUser();
    }

    @Override
    public List<ProductOrder> getAllProduct() {
        return baseMapper.getAllProduct();
    }

    @Override
    @Transactional
    public boolean saveOrder(ProductOrder order, List<Long> sysFileIds) {
        if (order.getId() == null) {
            save(order);
        } else {
            LambdaUpdateWrapper<ProductOrder> updateWrapper = new LambdaUpdateWrapper<ProductOrder>();
            updateWrapper.eq(ProductOrder::getId, order.getId());
            updateWrapper
                    .set(ProductOrder::getChangedBy, order.getChangedBy())
                    .set(ProductOrder::getOrderState, order.getOrderState())
                    .set(ProductOrder::getProductId, order.getProductId())
                    .set(ProductOrder::getProductTermId, order.getProductTermId())
                    .set(ProductOrder::getProductName, order.getProductName())
                    .set(ProductOrder::getProductTermName, order.getProductTermName())
                    .set(ProductOrder::getCertificateType, order.getCertificateType())
                    .set(ProductOrder::getCertificateImg, order.getCertificateImg())
                    .set(ProductOrder::getCustomerName, order.getCustomerName())
                    .set(ProductOrder::getCertificateNo, order.getCertificateNo())
                    .set(ProductOrder::getContactTelephone, order.getContactTelephone())
                    .set(ProductOrder::getVehicleLicenseImg, order.getVehicleLicenseImg())
                    .set(ProductOrder::getVinNo, order.getVinNo())
                    .set(ProductOrder::getEngineNo, order.getEngineNo())
                    .set(ProductOrder::getCarNo, order.getCarNo())
                    .set(ProductOrder::getRegistrationDate, order.getRegistrationDate())
                    .set(ProductOrder::getIssueDate, order.getIssueDate())
                    .set(ProductOrder::getCarUseage, order.getCarUseage())
                    .set(ProductOrder::getCarBrandId, order.getCarBrandId())
                    .set(ProductOrder::getCarSeriesId, order.getCarSeriesId())
                    .set(ProductOrder::getCarSeriesName, order.getCarSeriesName())
                    .set(ProductOrder::getPowerType, order.getPowerType())
                    .set(ProductOrder::getCarInvoiceImg, order.getCarInvoiceImg())
                    .set(ProductOrder::getCarPrice, order.getCarPrice())
                    .set(ProductOrder::getCarBuyDate, order.getCarBuyDate())
                    .set(ProductOrder::getLeftImg, order.getLeftImg())
                    .set(ProductOrder::getRightImg, order.getRightImg())
                    .set(ProductOrder::getLeftbackImg, order.getLeftbackImg())
                    .set(ProductOrder::getRightbackImg, order.getRightbackImg())
                    .set(ProductOrder::getVinImg, order.getVinImg())
                    .set(ProductOrder::getCarMileageImg, order.getCarMileageImg())
                    .set(ProductOrder::getCarMileage, order.getCarMileage())
                    .set(ProductOrder::getVehicleTaxPrice, order.getVehicleTaxPrice())
                    .set(ProductOrder::getPurchaseTaxPrice, order.getPurchaseTaxPrice())
                    .set(ProductOrder::getPurchaseTaxCompleteImg, order.getPurchaseTaxCompleteImg())
                    .set(ProductOrder::getVehicleLicensePrice, order.getVehicleLicensePrice())
                    .set(ProductOrder::getVehicleLicenseInvoiceImg, order.getVehicleLicenseInvoiceImg())
                    .set(ProductOrder::getTrafficInsuranceImg, order.getTrafficInsuranceImg())
                    .set(ProductOrder::getServiceEnableDate, order.getServiceEnableDate())
                    .set(ProductOrder::getServiceDisableDate, order.getServiceDisableDate())
                    .set(ProductOrder::getServicePeriod, order.getServicePeriod())
                    .set(ProductOrder::getStoreId, order.getStoreId())
                    .set(ProductOrder::getStoreName, order.getStoreName())
                    .set(ProductOrder::getSuggestPrice, order.getSuggestPrice())
                    .set(ProductOrder::getPayType, order.getPayType())
                    .set(ProductOrder::getPayNo, order.getPayNo())
                    .set(ProductOrder::getPayPrice, order.getPayPrice())
                    .set(ProductOrder::getPayImg, order.getPayImg())
                    .set(ProductOrder::getPayPeriod, order.getPayPeriod())
                    .set(ProductOrder::getPayFirstDate, order.getPayFirstDate())
                    .set(ProductOrder::getRemark, order.getRemark())
                    .set(ProductOrder::getSignState, null);
            update(updateWrapper);
        }
        if (CollectionUtils.isNotEmpty(sysFileIds)) {
            List<SysFile> sysFileList = sysFileService.selectSysFileByIds(sysFileIds.toArray(new Long[]{}));
            for (SysFile sysFile : sysFileList) {
                if (sysFile.getRelatedId() == null) {
                    sysFile.setRelatedId(order.getId());
                    sysFile.setUpdateTime(new Date());
                    if (order.getId() == null) {
                        sysFile.setUpdateBy(order.getCreatedBy());
                    } else {
                        sysFile.setUpdateBy(order.getChangedBy());
                    }
                    sysFileService.updateSysFile(sysFile);
                }
            }
        }
        return true;
    }

    @Override
    @Transactional
    public boolean audit(ProductOrderVo param) {
        ProductOrder order = getById(param.getId());
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        //待审核和审核通过但未签署的可以审核
        if (!(ProductOrderVo.OrderState.WAIT_AUDIT.equals(order.getOrderState())) && !(
                ProductOrderVo.OrderState.PASS.equals(order.getOrderState()) && (ProductOrderVo.SignState.NO_SIGN.equals(order.getSignState())
                        || ProductOrderVo.SignState.SIGN_FAIL.equals(order.getSignState())))) {
            throw new BaseException("该订单状态不能审核");
        }
        if (ProductOrderVo.OrderState.REJECT.equals(param.getOrderState())) {
            order.setAuditReason(param.getAuditReason());
        } else if (ProductOrderVo.OrderState.PASS.equals(param.getOrderState())) {
            order.setAuditReason("");
            order.setSignState(ProductOrderVo.SignState.NO_SIGN);
            productOrderInsuranceService.push(param.getId(), SecurityUtils.getLoginUser().getUsername());
        } else {
            throw new BaseException("参数错误");
        }
        order.setOrderState(param.getOrderState());
        order.setChangedBy(SecurityUtils.getLoginUser().getUsername());

        order.setAuditUserId(SecurityUtils.getLoginUser().getUserId());
        order.setAuditUserName(SecurityUtils.getLoginUser().getUsername());
        order.setAuditTime(new Date());
        return updateById(order);
    }
}
