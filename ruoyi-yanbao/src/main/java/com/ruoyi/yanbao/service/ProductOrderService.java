package com.ruoyi.yanbao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.yanbao.entity.ProductOrder;
import com.ruoyi.yanbao.entity.vo.ProductOrderVo;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface ProductOrderService extends IService<ProductOrder> {

    List<ProductOrder> getAllSaleUser();

    List<ProductOrder> getAllProduct();

    boolean saveOrder(ProductOrder order, List<Long> sysFileIds);

    boolean audit(ProductOrderVo param);
}
