package com.ruoyi.yanbao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 投保管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@TableName("p_product_order_insurance")
public class ProductOrderInsurance implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    private String createdBy;

    @TableField(fill = FieldFill.UPDATE)
    private Date changedAt;

    private String changedBy;

    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDelete;

    /**
     * 订单ID
     */
    private Long productOrderId;

    /**
     * 投保状态：0：待投保，1：已投保，2：退保
     */
    private Integer insuranceState;

    /**
     * 保单号
     */
    private String insuranceNo;

    /**
     * 投保时间
     */
    private Date insuranceDate;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 投保金额
     */
    private BigDecimal price;

    /**
     * 保单文件
     */
    private String insuranceFile;

    /**
     * 保险公司ID
     */
    private Long insuranceCompanyId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getChangedAt() {
        return changedAt;
    }

    public void setChangedAt(Date changedAt) {
        this.changedAt = changedAt;
    }

    public String getChangedBy() {
        return changedBy;
    }

    public void setChangedBy(String changedBy) {
        this.changedBy = changedBy;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Long getProductOrderId() {
        return productOrderId;
    }

    public void setProductOrderId(Long productOrderId) {
        this.productOrderId = productOrderId;
    }

    public Integer getInsuranceState() {
        return insuranceState;
    }

    public void setInsuranceState(Integer insuranceState) {
        this.insuranceState = insuranceState;
    }

    public String getInsuranceNo() {
        return insuranceNo;
    }

    public void setInsuranceNo(String insuranceNo) {
        this.insuranceNo = insuranceNo;
    }

    public Date getInsuranceDate() {
        return insuranceDate;
    }

    public void setInsuranceDate(Date insuranceDate) {
        this.insuranceDate = insuranceDate;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getInsuranceFile() {
        return insuranceFile;
    }

    public void setInsuranceFile(String insuranceFile) {
        this.insuranceFile = insuranceFile;
    }

    public Long getInsuranceCompanyId() {
        return insuranceCompanyId;
    }

    public void setInsuranceCompanyId(Long insuranceCompanyId) {
        this.insuranceCompanyId = insuranceCompanyId;
    }

    @Override
    public String toString() {
        return "ProductOrderInsurance{" +
            "id = " + id +
            ", createdAt = " + createdAt +
            ", createdBy = " + createdBy +
            ", changedAt = " + changedAt +
            ", changedBy = " + changedBy +
            ", isDelete = " + isDelete +
            ", productOrderId = " + productOrderId +
            ", insuranceState = " + insuranceState +
            ", insuranceNo = " + insuranceNo +
            ", insuranceDate = " + insuranceDate +
            ", beginTime = " + beginTime +
            ", endTime = " + endTime +
            ", price = " + price +
            ", insuranceFile = " + insuranceFile +
            ", insuranceCompanyId = " + insuranceCompanyId +
            "}";
    }
}
