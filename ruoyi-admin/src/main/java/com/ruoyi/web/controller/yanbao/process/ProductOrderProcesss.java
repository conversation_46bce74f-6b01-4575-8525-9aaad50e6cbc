package com.ruoyi.web.controller.yanbao.process;

import com.ruoyi.common.utils.OssUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.yanbao.entity.ProductOrder;
import com.ruoyi.yanbao.entity.vo.ProductOrderVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ProductOrderProcesss {

    public List<Long> processOrderFile(ProductOrder po, ProductOrderVo order) {
        List<Long> sysFileIds = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        ids = getFileIds(order.getCertificateImgList(), sysFileIds);
        po.setCertificateImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getVehicleLicenseImgList(), sysFileIds);
        po.setVehicleLicenseImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getCarInvoiceImgList(), sysFileIds);
        po.setCarInvoiceImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getLeftImgList(), sysFileIds);
        po.setLeftImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getRightImgList(), sysFileIds);
        po.setRightImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getLeftbackImgList(), sysFileIds);
        po.setLeftbackImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getRightbackImgList(), sysFileIds);
        po.setRightbackImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getVinImgList(), sysFileIds);
        po.setVinImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getCarMileageImgList(), sysFileIds);
        po.setCarMileageImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getPurchaseTaxCompleteImgList(), sysFileIds);
        po.setPurchaseTaxCompleteImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getVehicleLicenseInvoiceImgList(), sysFileIds);
        po.setVehicleLicenseInvoiceImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getTrafficInsuranceImgList(), sysFileIds);
        po.setTrafficInsuranceImg(StringUtils.join(ids, ","));
        ids = getFileIds(order.getPayImgList(), sysFileIds);
        po.setPayImg(StringUtils.join(ids, ","));
        return sysFileIds;
    }

    private List<Long> getFileIds(List<OssUtil.FileItem> fileItems, List<Long> sysFileIds) {
        List<Long> ids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fileItems)) {
            for (OssUtil.FileItem fileItem : fileItems) {
                ids.add(fileItem.getId());
                sysFileIds.add(fileItem.getId());
            }
        }
        return ids;
    }
}
