<template>
  <div>
    <viewer :images="images">
      <img class="image-label" v-for="img in images" :src="img"></img>
    </viewer>
  </div>
</template>

<script setup>

const props = defineProps({
  imgList: {
    type: Array,
    default: []
  },
})

const images = ref([])

watch(() => props.imgList, val => {
  if (val) {
    let list=[];
    val.forEach(item => {list.push(item.url)})
    images.value = list;
  } else {
    images.value = []
  }
},{ deep: true, immediate: true })


</script>

<style scoped lang="scss">
  .image-label {
    width: 100px;
    height: 100px;
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;
  }
</style>
