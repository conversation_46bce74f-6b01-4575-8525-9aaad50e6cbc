package com.ruoyi.yanbao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.ProductOrder;
import com.ruoyi.yanbao.entity.ProductOrderInsurance;
import com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceVo;
import com.ruoyi.yanbao.mapper.ProductMapper;
import com.ruoyi.yanbao.mapper.ProductOrderInsuranceMapper;
import com.ruoyi.yanbao.mapper.ProductOrderMapper;
import com.ruoyi.yanbao.service.ProductOrderInsuranceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 投保管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class ProductOrderInsuranceServiceImpl extends ServiceImpl<ProductOrderInsuranceMapper, ProductOrderInsurance> implements ProductOrderInsuranceService {

    @Autowired
    private ProductOrderMapper productOrderMapper;

    @Autowired
    private ProductMapper productMapper;

    @Override
    public ProductOrderInsurance push(Long productOrderId, String createdBy) {
        if (productOrderId != null) {
            ProductOrderInsurance productOrderInsurance = baseMapper.selectOne(new LambdaQueryWrapper<ProductOrderInsurance>()
                    .eq(ProductOrderInsurance::getProductOrderId, productOrderId));
            if (productOrderInsurance != null) {
                return productOrderInsurance;
            }

            ProductOrder order = productOrderMapper.selectById(productOrderId);
            if (order != null) {
                Product product = productMapper.selectById(order.getProductId());
                if (product.getInsuranceCompanyId() != null) {
                    ProductOrderInsurance item = new ProductOrderInsurance();
                    item.setCreatedAt(new Date());
                    item.setCreatedBy(StringUtils.isNotBlank(createdBy) ? createdBy : SecurityUtils.getLoginUser().getUsername());
                    item.setIsDelete(Constants.NOT_DELETE);
                    item.setProductOrderId(productOrderId);
                    item.setInsuranceCompanyId(product.getInsuranceCompanyId());
                    item.setInsuranceState(ProductOrderInsuranceVo.InsuranceState.WAIT);
                    save(item);
                    return item;
                }
            }
        }
        return null;
    }

    @Override
    public List<ProductOrderInsuranceVo> queryList(ProductOrderInsuranceVo param) {
        return baseMapper.queryList(param);
    }
}
