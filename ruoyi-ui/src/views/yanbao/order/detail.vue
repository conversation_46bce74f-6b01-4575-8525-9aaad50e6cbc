<template>
  <div>
    <el-form :model="form" label-width="120px">
      <el-card>
        <div slot="header" class="clearfix">
          <span>订单详情（订单号：{{ form.id }}）</span>
        </div>
        <el-divider></el-divider>
        <el-row>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="订单状态：">
              <span v-if="form.orderState==0"><el-tag type="info">暂存</el-tag></span>
              <span v-else-if="form.orderState==1"><el-tag>待审核</el-tag></span>
              <span v-else-if="form.orderState==2"><el-tag type="danger">驳回</el-tag></span>
              <span v-else-if="form.orderState==3"><el-tag type="success">审核通过</el-tag></span>
              <span v-else><el-tag type="warning">未知</el-tag></span>
            </el-form-item>
          </el-col>
          <el-col v-if="form.orderState==2" :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="驳回原因：">
              <span>{{ form.auditReason }}</span>
            </el-form-item>
          </el-col>
          <el-col v-if="form.orderState!=0 && form.orderState!=1" :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="审核人：">
              <span>{{ form.auditUserName }}</span>
            </el-form-item>
          </el-col>
          <el-col v-if="form.orderState!=0 && form.orderState!=1" :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="审核时间：">
              <span>{{ parseTime(form.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="出单状态：">
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="业务员：">
              <span>{{ form.saleUserName }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="产品名称：">
              <span>{{ form.productName }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="服务期限(月)：">
              <span>{{ form.servicePeriod }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="服务生效日期：">
              <span>{{ parseTime(form.serviceEnableDate, '{y}-{m}-{d}') }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="服务结束日期：">
              <span>{{ parseTime(form.serviceDisableDate, '{y}-{m}-{d}') }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="经销商：">
              <span>{{ form.storeName }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="付款方式：">
              <span v-if="form.payType==1">线下付款</span>
              <span v-else-if="form.payType==2">分期</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="付款金额：">
              <span>{{ formatMoney(form.payPrice) }}</span>
            </el-form-item>
          </el-col>
          <el-col v-if="form.payType==1" :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="付款小票单号：">
              <span>{{ form.payNo }}</span>
            </el-form-item>
          </el-col>
          <el-col v-if="form.payType==1" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="付款小票图片：">
              <ImageViewer :imgList="form.payImgList"></ImageViewer>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="车主姓名：">
              <span>{{ form.customerName }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="联系电话：">
              <span>{{ form.contactTelephone }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="证件类型：">
              <span v-if="form.certificateType==1">身份证</span>
              <span v-else-if="form.certificateType==2">营业执照</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="证件号：">
              <span>{{ form.certificateNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="证件图片：">
              <ImageViewer :imgList="form.certificateImgList"></ImageViewer>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="车架号：">
              <span>{{ form.vinNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="发动机号：">
              <span>{{ form.engineNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="车牌号码：">
              <span>{{ form.carNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="注册日期：">
              <span>{{ parseTime(form.registrationDate, '{y}-{m}-{d}') }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="发证日期：">
              <span>{{ parseTime(form.issueDate, '{y}-{m}-{d}') }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="使用性质：">
              <span>{{ getCarUsageText(form.carUseage) }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="车辆品牌：">
              <span>{{ form.carBrandName }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="车辆型号：">
              <span>{{ form.carSeriesName }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="动力类型：">
              <span>{{ getPowerTypeText(form.powerType) }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="行驶证图片：">
              <ImageViewer :imgList="form.vehicleLicenseImgList"></ImageViewer>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="车辆图片：">
              <ImageViewer :imgList="form.carImgList"></ImageViewer>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="购车发票：">
              <ImageViewer :imgList="form.carInvoiceImgList"></ImageViewer>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="购车金额：">
              <span>{{ formatMoney(form.carPrice) }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="购车日期：">
              <span>{{ parseTime(form.carBuyDate, '{y}-{m}-{d}') }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="里程图片：">
              <ImageViewer :imgList="form.carMileageImgList"></ImageViewer>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="行驶里程：">
              <span>{{ form.carMileage }} 公里</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="车船税金额：">
              <span>{{ formatMoney(form.vehicleTaxPrice) }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="购置税金额：">
              <span>{{ formatMoney(form.purchaseTaxPrice) }}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="车辆上牌费用：">
              <span>{{ formatMoney(form.vehicleLicensePrice) }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="保单图片：">
              <ImageViewer :imgList="form.trafficInsuranceImgList"></ImageViewer>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="完税证明：">
              <ImageViewer :imgList="form.purchaseTaxCompleteImgList"></ImageViewer>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="上牌费用发票：">
              <ImageViewer :imgList="form.vehicleLicenseInvoiceImgList"></ImageViewer>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注：">
              <span>{{ form.remark || '无' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div style="text-align: center;padding-top: 10px">
      <el-button @click="audit" v-if="form.orderState==1||(form.orderState==3&&(form.signState==0||form.signState==3))"
                 v-hasPermi="['yanbao:order:audit']" el-button type="primary" style="width: 30%; text-align: center">{{form.orderState==1?'审核':'驳回'}}
      </el-button>
    </div>
    <div style="height: 50px"></div>
    <el-dialog title="审核" v-model="open" width="80%" append-to-body>
      <el-form ref="orderAuditRef" :model="orderAuditForm" :rules="orderAuditRules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="审核状态" prop="orderState">
              <el-select v-model="orderAuditForm.orderState" placeholder="请选择审核状态" clearable>
                <el-option v-if="form.orderState==1" label="通过" :value="3"/>
                <el-option label="驳回" :value="2"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="orderAuditForm.orderState==2" :span="24">
            <el-form-item label="驳回原因" prop="auditReason">
              <el-input placeholder="请输入驳回原因" :rows="3" maxlength="100" show-word-limit type="textarea"
                        v-model="orderAuditForm.auditReason"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>

:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 100px !important;
}

:deep(.el-upload-list--picture-card) {
  --el-upload-list-picture-card-size: 100px !important;
}

:deep(.avatar) {
  width: 100px;
  height: 100px;
  display: block;
}
</style>

<script setup name="OrderDetail">
import {getOrder, auditOrder} from "@/api/yanbao/productOrder"
import {getProductInfo, getProductSuitStore} from "@/api/yanbao/product"
import {parseTime} from '@/utils/ruoyi'

const route = useRoute()
const router = useRouter()
const {proxy} = getCurrentInstance()

const data = reactive({
  form: {},
  open: false,
  orderAuditForm: {},
  orderAuditRules: {
    orderState: [{required: true, message: "审核状态不能为空", trigger: "change"}],
    auditReason: [{required: true, message: "驳回原因不能为空", trigger: "blur"}],
  },
})

const {form, orderAuditForm, orderAuditRules, open} = toRefs(data)


// 使用性质转换
function getCarUsageText(value) {
  const usages = {
    '1': '非营运',
    '2': '营运'
  }
  return usages[value] || value
}

// 动力类型转换
function getPowerTypeText(value) {
  const types = {
    '1': '燃油（油混）汽车',
    '2': '纯电动汽车',
    '3': '插电混合动力汽车',
    '4': '增程式电动汽车'
  }
  return types[value] || value
}

// 金额格式化
function formatMoney(value) {
  if (value === null || value === undefined || value === '') {
    return '0.00元'
  }
  return parseFloat(value).toFixed(2) + '元'
}

// 返回上一页
function goBack() {
  router.back()
}

// 获取订单详情
function getOrderDetail() {
  const orderId = route.params.orderId || route.query.orderId
  if (!orderId) {
    proxy.$modal.msgError("订单ID不能为空")
    return
  }

  getOrder({orderId: orderId}).then(response => {
    form.value = response.data
  }).catch(() => {
    proxy.$modal.msgError("获取订单详情失败")
  })
}


function audit() {
  open.value = true;
}

function cancel() {
  open.value = false;
}

function submitForm() {
  proxy.$refs["orderAuditRef"].validate((valid) => {
    if (valid) {
      orderAuditForm.value.id = form.value.id;
      auditOrder(orderAuditForm.value).then(response => {
        proxy.$modal.msgSuccess("操作成功")
        open.value = false;
        getOrderDetail();
      })
    }
  })
}

// 页面加载时获取订单详情
getOrderDetail()
</script>
