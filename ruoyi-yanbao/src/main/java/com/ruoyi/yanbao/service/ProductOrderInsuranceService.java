package com.ruoyi.yanbao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.yanbao.entity.ProductOrderInsurance;
import com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceVo;

import java.util.List;

/**
 * <p>
 * 投保管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProductOrderInsuranceService extends IService<ProductOrderInsurance> {

    ProductOrderInsurance push(Long productOrderId, String createdBy);

    List<ProductOrderInsuranceVo> queryList(ProductOrderInsuranceVo param);
}
