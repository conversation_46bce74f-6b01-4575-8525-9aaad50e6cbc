<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="车主姓名" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入车主姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactTelephone">
        <el-input
          v-model="queryParams.contactTelephone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车架号" prop="vinNo">
        <el-input
            v-model="queryParams.vinNo"
            placeholder="请输入车架号"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车牌号" prop="carNo">
        <el-input
            v-model="queryParams.carNo"
            placeholder="请输入车牌号"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="orderState">
        <el-select style="width: 200px" v-model="queryParams.orderState" placeholder="请选择订单状态" clearable>
          <el-option label="暂存" :value="0" />
          <el-option label="待审核" :value="1" />
          <el-option label="驳回" :value="2" />
          <el-option label="审核通过" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="签署状态" prop="signState">
        <el-select style="width: 200px" v-model="queryParams.signState" placeholder="请选择签署状态" clearable>
          <el-option label="待签署" :value="0" />
          <el-option label="签署中" :value="1" />
          <el-option label="签署成功" :value="2" />
          <el-option label="签署失败" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务员" prop="saleUserId">
        <el-select style="width: 200px" v-model="queryParams.saleUserId" placeholder="请选择业务员" clearable>
          <el-option v-for="item in users" :key="item.saleUserId" :label="item.saleUserName" :value="item.saleUserId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="经销商" prop="storeId">
        <el-select style="width: 200px" v-model="queryParams.storeId" placeholder="请选择经销商" clearable>
          <el-option v-for="item in stores" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="产品" prop="productId">
        <el-select style="width: 200px" v-model="queryParams.productId" placeholder="请选择产品" clearable>
          <el-option v-for="item in products" :key="item.productId" :label="item.productName" :value="item.productId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" size="small">
      <el-table-column label="订单号" fixed align="center" prop="orderNo" width="100">
        <template #default="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.id }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="车牌号" align="center" prop="carNo" width="100"/>
      <el-table-column label="车架号" align="center" prop="vinNo" width="140"/>
      <el-table-column label="产品名称" align="center" prop="productName" width="150"/>
      <el-table-column label="车主姓名" align="center" prop="customerName" width="120" />
      <el-table-column label="联系电话" align="center" prop="contactTelephone" width="120" />
      <el-table-column label="服务生效时间" align="center" prop="serviceEnableDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.serviceEnableDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="服务结束时间" align="center" prop="serviceDisableDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.serviceDisableDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="业务员" align="center" prop="saleUserName" width="100" />
      <el-table-column label="订单状态" align="center" prop="orderState" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.orderState==0" :disable-transitions="true" :key="0" type="info">暂存</el-tag>
          <el-tag v-else-if="scope.row.orderState==1" :disable-transitions="true" :key="1">待审核</el-tag>
          <el-tag v-else-if="scope.row.orderState==2" :disable-transitions="true" :key="2" type="danger">驳回</el-tag>
          <el-tag v-else-if="scope.row.orderState==3" :disable-transitions="true" :key="3" type="success">审核通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="签署状态" align="center" prop="signState" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.signState==0" :disable-transitions="true" :key="0" type="info">待签署</el-tag>
          <el-tag v-else-if="scope.row.signState==1" :disable-transitions="true" :key="1">签署中</el-tag>
          <el-tag v-else-if="scope.row.signState==2" :disable-transitions="true" :key="2" type="success">签署成功</el-tag>
          <el-tag v-else-if="scope.row.signState==3" :disable-transitions="true" :key="3" type="danger">签署失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width" min-width="100">
        <template #default="scope">
          <el-button v-if="scope.row.orderState==0||scope.row.orderState==2" link type="primary" @click="handleAdd(scope.row)" v-hasPermi="['yanbao:order:add']">编辑</el-button>
          <el-button v-if="scope.row.orderState==1" link type="primary" @click="handleDetail(scope.row)" v-hasPermi="['yanbao:order:audit']">审核</el-button>
          <el-button v-if="scope.row.orderState==0||scope.row.orderState==2" link type="primary" @click="handleDelete(scope.row)" v-hasPermi="['yanbao:order:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="OrderList">
import { listOrder ,getAllSaleUser,getAllProduct,deleteOrder} from "@/api/yanbao/productOrder"
import {optionSelectStore} from "@/api/yanbao/store";
import { parseTime } from '@/utils/ruoyi';
const { proxy } = getCurrentInstance()
const router = useRouter()

const orderList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const users = ref([])
const stores = ref([])
const products= ref([])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: null,
    customerName: null,
    contactTelephone: null
  }
})

const { queryParams } = toRefs(data)

/** 查询订单列表 */
function getList() {
  loading.value = true
  listOrder(queryParams.value).then(response => {
    orderList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  router.push({
    path: "/yanbao/order/detail",
    query: { orderId: row.id }
  })
}
function handleAdd(row) {
  router.push({
    path: "/yanbao/order/add",
    query: { productId: row.productId,orderId: row.id }
  })
}
function handleDelete(row) {
  const id = row.id
  proxy.$modal.confirm('是否确认删除订单号为"' + id + '"的数据项？').then(function() {
    return deleteOrder({id:id})
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

getList()

getAllSaleUser().then(resp=>{
  users.value = resp.data;
})
optionSelectStore().then(resp=>{
  stores.value=resp.data;
})
getAllProduct().then(resp=>{
  products.value=resp.data;
})
</script>
