<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysFileMapper">

    <resultMap type="SysFile" id="SysFileResult">
        <result property="id" column="id"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createdAt" column="created_at"/>
        <result property="createdBy" column="created_by"/>
        <result property="path" column="path"/>
        <result property="name" column="name"/>
        <result property="size" column="size"/>
        <result property="type" column="type"/>
        <result property="relatedTable" column="related_table"/>
        <result property="relatedId" column="related_id"/>
    </resultMap>

    <sql id="selectSysFileVo">
        select id, is_delete, created_at, created_by, path, name, size, type, related_table, related_id from sys_file
    </sql>

    <select id="selectSysFileList" parameterType="SysFile" resultMap="SysFileResult">
        <include refid="selectSysFileVo"/>
        <where>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
            <if test="createdBy != null  and createdBy != ''">and created_by like concat('%', #{createdBy}, '%')</if>
            <if test="path != null  and path != ''">and path like concat('%', #{path}, '%')</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null">and type = #{type}</if>
            <if test="relatedTable != null  and relatedTable != ''">and related_table = #{relatedTable}</if>
            <if test="relatedId != null">and related_id = #{relatedId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(created_at,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(created_at,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectSysFileById" parameterType="Long" resultMap="SysFileResult">
        <include refid="selectSysFileVo"/>
        where id = #{id}
    </select>

    <select id="selectSysFileByRelated" resultMap="SysFileResult">
        <include refid="selectSysFileVo"/>
        where related_table = #{relatedTable} and related_id = #{relatedId} and is_delete = 0
        order by created_at desc
    </select>

    <select id="selectSysFileByPath" parameterType="String" resultMap="SysFileResult">
        <include refid="selectSysFileVo"/>
        where path = #{path} and is_delete = 0
    </select>

    <select id="selectSysFileByIds" resultMap="SysFileResult">
        <include refid="selectSysFileVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_delete = 0
        order by created_at desc
    </select>

    <insert id="insertSysFile" parameterType="SysFile" useGeneratedKeys="true" keyProperty="id">
        insert into sys_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDelete != null">is_delete,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="createdBy != null and createdBy != ''">created_by,</if>
            <if test="path != null and path != ''">path,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="size != null">size,</if>
            <if test="type != null">type,</if>
            <if test="relatedTable != null">related_table,</if>
            <if test="relatedId != null">related_id,</if>
            <if test="ocrType != null">ocr_type,</if>
            <if test="ocrResult != null">ocr_result,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="createdBy != null and createdBy != ''">#{createdBy},</if>
            <if test="path != null and path != ''">#{path},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="size != null">#{size},</if>
            <if test="type != null">#{type},</if>
            <if test="relatedTable != null">#{relatedTable},</if>
            <if test="relatedId != null">#{relatedId},</if>
            <if test="ocrType != null">#{ocrType},</if>
            <if test="ocrResult != null">#{ocrResult},</if>
        </trim>
    </insert>

    <update id="updateSysFile" parameterType="SysFile">
        update sys_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="createdBy != null and createdBy != ''">created_by = #{createdBy},</if>
            <if test="path != null and path != ''">path = #{path},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="size != null">size = #{size},</if>
            <if test="type != null">type = #{type},</if>
            <if test="relatedTable != null">related_table = #{relatedTable},</if>
            <if test="relatedId != null">related_id = #{relatedId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysFileById" parameterType="Long">
        update sys_file set is_delete = 1 where id = #{id}
    </delete>

    <delete id="deleteSysFileByIds" parameterType="String">
        update sys_file set is_delete = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
