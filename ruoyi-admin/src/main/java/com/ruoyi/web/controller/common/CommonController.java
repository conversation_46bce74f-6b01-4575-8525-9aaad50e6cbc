package com.ruoyi.web.controller.common;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.AliyunOcrUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.OssUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.system.domain.SysFile;
import com.ruoyi.system.service.ISysFileService;
import com.ruoyi.web.model.FileUploadModel;
import com.ruoyi.web.model.OcrResultModel;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class CommonController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    private static final String FILE_DELIMETER = ",";

    @Autowired
    private ISysFileService sysFileService;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.checkAllowDownload(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求（单个）
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 通用上传请求（多个）
     */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception {
        try {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files) {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;
                urls.add(url);
                fileNames.add(fileName);
                newFileNames.add(FileUtils.getName(fileName));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        try {
            if (!FileUtils.checkAllowDownload(resource)) {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + FileUtils.stripPrefix(resource);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    @PostMapping("/uploadOss")
    public AjaxResult uploadOssFile(FileUploadModel model) throws Exception {
        try {
            String objectName = FileUploadModel.convertDir(model.getRelatedTable()) + "/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
            OssUtil.FileItem fileItem = OssUtil.upload(model.getFile(), objectName);
            if (fileItem != null) {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("url", fileItem.getUrl());
                ajax.put("fileName", fileItem.getName());
                ajax.put("newFileName", FileUtils.getName(fileItem.getName()));
                ajax.put("originalFilename", model.getFile().getOriginalFilename());
                SysFile sysFile = new SysFile();
                sysFile.setPath(fileItem.getPath());
                sysFile.setName(fileItem.getName());
                sysFile.setSize(fileItem.getSize());
                sysFile.setRelatedTable(model.getRelatedTable());
                sysFile.setType(1);
                sysFile.setCreatedBy(getUsername());
                sysFile.setIsDelete(0);
                sysFile.setCreatedAt(new Date());
                sysFile.setOcrType(model.getOcrType());
                if (model.getOcrType() != null) {
                    JSONObject data = processOcr(model.getFile().getInputStream(), model.getOcrType(), ajax);
                    if (data != null) {
                        sysFile.setOcrResult(data.toJSONString());
                    }
                }
                sysFileService.insertSysFile(sysFile);
                ajax.put("id", sysFile.getId());
                return ajax;
            }
            return AjaxResult.error("文件上传失败");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    private JSONObject processOcr(InputStream is, Integer ocrType, AjaxResult ajax) {
        if (FileUploadModel.OCR_TYPE_ID_CARD.equals(ocrType)) {
            JSONObject data = AliyunOcrUtil.recognizeIdcard(is, null);
            if (data != null
                    && data.getJSONObject("face") != null
                    && data.getJSONObject("face").getJSONObject("data") != null) {
                JSONObject faceData = data.getJSONObject("face").getJSONObject("data");
                OcrResultModel ocrResultModel = new OcrResultModel();
                ocrResultModel.setCustomerName(faceData.getString("name"));
                ocrResultModel.setCertificateNo(faceData.getString("idNumber"));
                ajax.put("ocrResult", ocrResultModel);
                return faceData;
            }
        } else if (FileUploadModel.OCR_TYPE_VEHICLE_LICENSE.equals(ocrType)) {
            JSONObject data = AliyunOcrUtil.recognizeVehicleLicense(is, null);
            if (data != null
                    && data.getJSONObject("face") != null
                    && data.getJSONObject("face").getJSONObject("data") != null) {
                JSONObject faceData = data.getJSONObject("face").getJSONObject("data");
                OcrResultModel ocrResultModel = new OcrResultModel();
                ocrResultModel.setVinNo(faceData.getString("vinCode"));
                ocrResultModel.setEngineNo(faceData.getString("engineNumber"));
                ocrResultModel.setCarNo(faceData.getString("licensePlateNumber"));
                ocrResultModel.setRegistrationDate(formatDate(faceData.getString("registrationDate")));
                ocrResultModel.setIssueDate(formatDate(faceData.getString("issueDate")));
                if ("营运".equals(faceData.getString("useNature"))) {
                    ocrResultModel.setCarUseage(2);
                } else {
                    ocrResultModel.setCarUseage(1);
                }
                ajax.put("ocrResult", ocrResultModel);
                return faceData;
            }
        } else if (FileUploadModel.OCR_TYPE_BUSINESS_LICENSE.equals(ocrType)) {
            JSONObject data = AliyunOcrUtil.recognizeBusinessLicense(is, null);
            if (data != null) {
                OcrResultModel ocrResultModel = new OcrResultModel();
                ocrResultModel.setCustomerName(data.getString("companyName"));
                ocrResultModel.setCertificateNo(data.getString("creditCode"));
                ajax.put("ocrResult", ocrResultModel);
                return data;
            }
        }
        return null;
    }

    private Date formatDate(String date) {
        if (StringUtils.isNotBlank(date)) {
            try {
                return DateUtils.parseDate(date, "yyyy-MM-dd");
            } catch (Exception e) {

            }
        }
        return null;
    }
}
